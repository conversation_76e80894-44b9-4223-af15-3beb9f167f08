.tag {
  height: 40px;
  padding: 0 30px;
  margin: 0 5px;
  border: 1px solid #000;
  border-radius: 10px;
  cursor: pointer;
}

.checked {
  color: #1677ff;
  border-color: #1677ff;
}

.logoWrapper {
  .pointerCursor;
  display: flex;
  align-items: center;
  gap: 12px;
}

.appIcon {
  max-width: 36px;
  max-height: 28px;
  object-fit: contain;
  margin-top: 2px;
}

.appName {
  font-family: Inter;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  display: flex;
  align-items: center;
}

.radioGroup {
  & label {
    height: 40px;
    line-height: 40px;
    border: 0 !important;
    background-color: rgba(249, 249, 249, 1);
    font-weight: @fontWeight700;
    color: rgba(29, 25, 41, 1);
    &::before {
      display: none !important;
    }
  }
  :global(.ant-radio-button-wrapper-checked) {
    border-radius: 6px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark) {
    border-radius: 0px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark.first) {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark.last) {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
    & a {
      color: white;
    }
  }
}

.radioGroupDark {
  & label {
    height: 40px;
    line-height: 40px;
    border: 0 !important;
    background-color: rgba(249, 249, 249, 0.25);
    font-weight: @fontWeight700;
    color: rgba(29, 25, 41, 1);
    &::before {
      display: none !important;
    }
  }
  :global(.ant-radio-button-wrapper-checked) {
    border-radius: 6px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark) {
    border-radius: 0px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark.first) {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
    & a {
      color: white;
    }
  }
  :global(.ant-radio-button-wrapper-checked.dark.last) {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
    & a {
      color: white;
    }
  }
}

.ant-radio-button-wrapper-checked {
  border-radius: 6px !important;
}
.radioButtonIcon {
  vertical-align: middle;
  max-width: 15px;
  max-height: 15px;
}
